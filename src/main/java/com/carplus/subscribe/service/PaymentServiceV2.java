package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.*;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.db.mysql.entity.payment.AccountDetail;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.enums.finbus.TransactionItemCodeEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.feign.FinServiceBusClient;
import com.carplus.subscribe.feign.FinanceClient;
import com.carplus.subscribe.model.OrderPriceInfoCriteria;
import com.carplus.subscribe.model.PriceInfo;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.finance.ICBCRep;
import com.carplus.subscribe.model.finbus.SecurityChangeCarReq;
import com.carplus.subscribe.model.invoice.InvoiceNewRequest;
import com.carplus.subscribe.model.invoice.InvoiceUpdateRequest;
import com.carplus.subscribe.model.payment.AccountList;
import com.carplus.subscribe.model.payment.AdditionalData;
import com.carplus.subscribe.model.payment.PayAuthCardHolder;
import com.carplus.subscribe.model.payment.TradeHistory;
import com.carplus.subscribe.model.payment.req.*;
import com.carplus.subscribe.model.payment.resp.*;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.priceinfo.PriceInfoWrapper;
import com.carplus.subscribe.model.priceinfo.req.OrderPriceInfoRefundRequest;
import com.carplus.subscribe.model.priceinfo.req.OrderPriceInfoRefundRetryRequest;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.model.request.paymentinfo.PaymentInfoQueryRequest;
import com.carplus.subscribe.model.response.paymentinfo.PaymentInfoQueryResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.server.PaymentServer;
import com.carplus.subscribe.utils.CsvUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.SingletonMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static carplus.common.enums.etag.ETagFlow.RETURN_SUCCESS;
import static com.carplus.subscribe.enums.AccountType.Credit;
import static com.carplus.subscribe.enums.AccountType.Remit;
import static com.carplus.subscribe.enums.OrderStatus.CLOSE_WITH_SUB;
import static com.carplus.subscribe.enums.OrderStatus.CREDITED;
import static com.carplus.subscribe.enums.PayFor.SecurityDeposit;
import static com.carplus.subscribe.enums.PaymentCategory.PayAuth;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.ETag;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MileageFee;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Refund;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Service
@Slf4j
public class PaymentServiceV2 {

    @Autowired
    private OrderPriceInfoRepository orderPriceInfoRepository;
    @Autowired
    private ContractService contractService;
    @Autowired
    private CheckoutService checkoutService;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private PaymentServer paymentServer;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private StationService stationService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyToCService notifyToCService;
    @Autowired
    private InvoiceServiceV2 invoiceService;
    @Autowired
    private CrsService crsService;
    @Autowired
    private PaymentInfoRepository paymentInfoRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private AccountDetailRepository accountDetailRepository;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ThreadPoolTaskExecutor executor;
    @Value("${station.subscribe}")
    private String subscribeStationCode;
    @Autowired
    private EtagInfoRepository etagInfoRepository;
    @Autowired
    private ETagService eTagService;
    @Autowired
    private FinServiceBusClient finServiceBusClient;
    @Autowired
    private FinanceClient financeClient;


    /**
     * 拿取訂單所有付款資訊
     */
    public List<PaymentInfo> getPaymentsByOrder(String orderNo) {
        return paymentInfoRepository.getPaymentInfosByOrderNo(orderNo);
    }

    public PaymentInfo getSecurityDepositPaymentOrElseThrow(String orderNo) {
        Orders order = orderService.getOrder(orderNo);
        // 若為續約訂單，則改用母單編號
        if (!order.getIsNewOrder()) {
            OrderPriceInfo securityDepositOrderPriceInfo = orderService.getSecurityDepositOrderPriceInfo(order.getContract().getMainContract());
            orderNo = securityDepositOrderPriceInfo.getOrderNo();
        }
        return getPaymentsByOrder(orderNo).stream()
            .filter(paymentInfo -> PayAuth.equals(paymentInfo.getPaymentCategory()) && SecurityDeposit.equals(paymentInfo.getPayFor()))
            .findFirst().orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT));
    }

    public List<PaymentInfo> getPaymentInfosByTradeId(String tradeId) {
        return paymentInfoRepository.getPaymentInfosByTradeId(tradeId);
    }

    /**
     * 保證金付款
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public PayAuthResponse paySecurityDeposit(PayAuthRequest payAuthRequest, String mainContractNo, int acctId) {
        MainContract mainContract = Optional.ofNullable(contractService.getMainContractById(acctId, mainContractNo)).orElseThrow(() -> new BadRequestException("查無此訂閱車合約"));

        // 目前只有第一次會付保證金，換車則換約
        Orders order = orderService.getOrdersByContractNo(mainContract.getContracts().get(0).getContractNo()).get(0);
        if (order.getStatus() < CREDITED.getStatus()) {
            throw new BadRequestException("訂閱車授信狀態未通過");
        }
        if (order.getStatus() > CLOSE_WITH_SUB.getStatus()) {
            throw new BadRequestException("訂單已結案");
        }
        if (order.getStatus() > CREDITED.getStatus()) {
            throw new BadRequestException("訂單保證金已付款");
        }

        OrderPriceInfo orderPriceInfo = priceInfoService.getPriceInfosByOrderAndAcctId(order.getOrderNo(), acctId)
            .stream().filter(p -> p.getCategory() == PriceInfoDefinition.PriceInfoCategory.SecurityDeposit
                && p.getPaymentId() == null).findAny().orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

        if (!orderPriceInfo.getAmount().equals(payAuthRequest.getAmount())) {
            throw new BadRequestException("支付的保證金金額有誤");
        }
        if (System.currentTimeMillis() > orderPriceInfo.getLastPayDate().toEpochMilli()) {
            contractService.paySecurityDepositTimeout(mainContract);
            throw new SubscribeException(SubscribeHttpExceptionCode.SECURITY_DEPOSIT_PAY_TIMEOUT);
        }
        // 鎖車
        Cars mainCar = carsService.findByPlateNo(mainContract.getPlateNo());
        orderService.lockCarAfterOrderPay(order, mainCar);

        // 前往付款
        PayAuthResponse payAuthResponse = null;
        try {
            if (payAuthRequest.getType() == PayAuthRequest.PayType.prime) {
                payAuthRequest.setCardholder(new PayAuthCardHolder(authServer.getUserWithRetry(acctId)));
            }
            payAuthRequest.setAcctId(acctId);
            payAuthRequest.setOrderNo(orderPriceInfo.getOrderNo());
            payAuthRequest.setPayFor(SecurityDeposit.name());
            payAuthRequest.setBusinessType(payAuthRequest.getBusinessType());
            payAuthRequest.setThreeDomainSecure(true);
            payAuthRequest.setAdditionalData(objectMapper.writeValueAsString(new AdditionalData(Collections.singletonList(orderPriceInfo.getId()))));
            payAuthResponse = paymentServer.pay(payAuthRequest);
        } catch (Exception e) {
            log.error("呼叫 payment-service 付款異常：{}", e.getMessage());
        }
        if (payAuthResponse == null || payAuthResponse.getStatusCode() != 0) {
            // 取消鎖車
            if (!mainCar.isVirtualCar()) {
                carsService.updateStatus(mainCar, CarDefine.CarStatus.Free);

                throw new SubscribeException(CAR_LOCK_FAIL);
            }
        }
        return payAuthResponse;
    }

    /**
     * 付款
     */
    public PayAuthResponse pay(PayAuthRequest payAuthRequest, String orderNo, int acctId) {
        orderService.getUserOrder(orderNo, acctId);
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        List<OrderPriceInfo> unpaidOrderPriceInfoList = priceInfoWrapper.getUnpaidAvailable().excludeType(Refund).getList().stream().filter(orderPriceInfo ->
                !MileageFee.equals(orderPriceInfo.getCategory()) || (orderPriceInfo.getType() != Pay.getCode() || orderPriceInfo.getInfoDetail().getEndMileage() != null))
            .filter(orderPriceInfo -> {
                    // Etag過濾，因有可Etag打遠通出還車正常，費用為0
                    if (!orderPriceInfo.getCategory().equals(ETag)) {
                        return true;
                    } else {
                        ETagInfo eTagInfo = etagInfoRepository.getEtagInfoByOrderPriceId(orderPriceInfo.getId());
                        if (eTagInfo == null) {
                            return false;
                        }
                        return Objects.equals(eTagInfo.getETagFlow(), RETURN_SUCCESS.getCode()) || orderPriceInfo.getAmount() > 0;
                    }
                }
            ).filter(orderPriceInfo ->
                // 若有審核，則需通過
                StringUtils.isBlank(orderPriceInfo.getUid())
                    || (StringUtils.isNotBlank(orderPriceInfo.getUid())
                    && Optional.ofNullable(orderPriceInfo.getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElseThrow(() -> new SubscribeException(ORDER_PRICE_NOT_REPLY_YET)))
            ).collect(Collectors.toList());
        Map<Integer, Set<Integer>> negativeOrderPriceInfoMap = priceInfoWrapper.getList()
            .stream()
            .filter(opi -> opi.getRefPriceInfoNo() != null && !opi.isPaid())
            .collect(Collectors.groupingBy(
                OrderPriceInfo::getRefPriceInfoNo,
                Collectors.mapping(OrderPriceInfo::getId, Collectors.toSet())
            ));

        Set<Integer> orderPriceInfoIds = unpaidOrderPriceInfoList.stream()
            .map(OrderPriceInfo::getId).collect(Collectors.toSet());
        // 檢查款項是否有問題
        orderPriceInfoIds = validatePayAuthPriceInfos(payAuthRequest, unpaidOrderPriceInfoList, negativeOrderPriceInfoMap, orderPriceInfoIds);
        // 檢查金額是否相符
        priceInfoService.checkAmount(new ArrayList<>(orderPriceInfoIds), payAuthRequest.getAmount());

        // 前往付款
        PayAuthResponse payAuthResponse = null;
        try {
            if (payAuthRequest.getType() == PayAuthRequest.PayType.prime) {
                payAuthRequest.setCardholder(new PayAuthCardHolder(authServer.getUserWithRetry(acctId)));
            }
            payAuthRequest.setAcctId(acctId);
            payAuthRequest.setOrderNo(orderNo);
            payAuthRequest.setPayFor(payAuthRequest.getPayFor());
            payAuthRequest.setBusinessType(payAuthRequest.getBusinessType());
            payAuthRequest.setThreeDomainSecure(true);
            payAuthRequest.setAdditionalData(objectMapper.writeValueAsString(new AdditionalData(new ArrayList<>(orderPriceInfoIds))));
            payAuthResponse = paymentServer.pay(payAuthRequest);
        } catch (Exception e) {
            log.error("呼叫 payment-service 付款異常：{}", e.getMessage());
        }
        if (payAuthResponse == null || payAuthResponse.getStatusCode() != 0) {
            log.error("付款失敗.Request:{}, Response:{}", payAuthRequest, payAuthResponse == null ? "null" : payAuthResponse);
        }

        return payAuthResponse;
    }

    /**
     * 檢驗款項是否有找不到或是有付款款項，但是有折扣款項並沒一起被選擇付款(導致折扣會變成退款)
     */
    private Set<Integer> validatePayAuthPriceInfos(PayAuthRequest payAuthRequest, List<OrderPriceInfo> unpaidOrderPriceInfoList, Map<Integer, Set<Integer>> negativeOrderPriceInfoMap, Set<Integer> orderPriceInfoIds) {
        if (!CollectionUtils.isEmpty(payAuthRequest.getOrderPriceInfoIds())) {
            Map<Integer, OrderPriceInfo> orderPriceInfoMap = unpaidOrderPriceInfoList.stream().collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));
            payAuthRequest.getOrderPriceInfoIds().forEach(id -> {
                OrderPriceInfo opi = orderPriceInfoMap.get(id);
                if (opi == null) {
                    throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
                }
                Set<Integer> negativeOrderPriceInfoIds = negativeOrderPriceInfoMap.get(opi.getId());
                if (CollectionUtils.isNotEmpty(negativeOrderPriceInfoIds) && !payAuthRequest.getOrderPriceInfoIds().containsAll(negativeOrderPriceInfoIds)) {
                    throw new SubscribeException(ORDER_PRICE_INFO_REFERENCE_NEED_SELECT);
                }
            });
            orderPriceInfoIds = payAuthRequest.getOrderPriceInfoIds();
        }
        return orderPriceInfoIds;
    }

    /**
     * 退款
     */
    public List<OrderPriceInfo> refundOrderPriceInfos(OrderPriceInfoRefundRequest orderPriceInfoRefundRequest) throws JsonProcessingException {
        refundRequestValidate(orderPriceInfoRefundRequest);
        Map<String, RefundRequest> refundRequestMap = new HashMap<>();
        List<OrderPriceInfo> refundOrderPriceInfos = new ArrayList<>();
        for (OrderPriceInfoRefundRequest.OrderPriceInfoRefund refund : orderPriceInfoRefundRequest.getRefunds()) {
            OrderPriceInfo refundOrderPriceInfo = priceInfoService.storedRefundRecord(refund.getOrderPriceInfoId(), refund.getRefundAmount(), null);
            String key = refundOrderPriceInfo.getOrderNo() + refundOrderPriceInfo.getRecTradeId();
            RefundRequest refundRequest = refundRequestMap.get(key);

            if (refundRequest == null) {
                refundRequest = new RefundRequest();
                refundRequest.setOrderNo(refundOrderPriceInfo.getOrderNo());
                refundRequest.setTradeId(refundOrderPriceInfo.getRecTradeId());
                refundRequest.setAdditionalDataObject(new AdditionalData(new ArrayList<>()));
                refundRequest.setAmount(refundOrderPriceInfo.getAmount());

            } else {
                refundRequest.setAmount(refundRequest.getAmount() + refundOrderPriceInfo.getAmount());
            }
            refundRequest.getAdditionalDataObject().getOrderPriceInfoIds().add(refundOrderPriceInfo.getId());
            refundRequestMap.put(key, refundRequest);

        }
        for (RefundRequest refundRequest : refundRequestMap.values()) {
            refundRequest.setAdditionalData(objectMapper.writeValueAsString(refundRequest.getAdditionalDataObject()));
            asyncRefund(refundRequest);
        }
        return refundOrderPriceInfos;
    }

    /**
     * 嘗試重新發動退款
     */
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void refundOrderPriceInfosRetry(OrderPriceInfoRefundRetryRequest orderPriceInfoRefundRetryRequest) throws JsonProcessingException {
        refundOrderPriceInfosRetry(orderPriceInfoRefundRetryRequest.getRefundIds());
    }

    /**
     * 嘗試重新發動退款
     *
     * @param orders                訂單
     * @param ignoreSecurityDeposit 是否忽略保證金
     */
    public void refundOrderPriceInfosRetry(Orders orders, boolean ignoreSecurityDeposit) {

        List<OrderPriceInfo> orderPirceInfoList = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder()
                .orderNo(Collections.singletonList(orders.getOrderNo()))
                .type(PriceInfoDefinition.PriceInfoType.Refund.getCode()).build()).stream()
            .filter(orderPriceInfo -> Math.abs(orderPriceInfo.getActualPrice()) > 0
                && orderPriceInfo.getPaymentId() == null).collect(Collectors.toList());
        if (ignoreSecurityDeposit) {
            orderPirceInfoList =
                orderPirceInfoList.stream()
                    .filter(orderPriceInfo -> !Objects.equals(
                        Optional.ofNullable(orderPriceInfo.getRefPriceInfoNo())
                            .map(refId -> priceInfoService.get(refId))
                            .map(OrderPriceInfo::getCategory)
                            .orElse(orderPriceInfo.getCategory()),
                        PriceInfoDefinition.PriceInfoCategory.SecurityDeposit))
                    .collect(Collectors.toList());
        }
        List<Integer> ids = orderPirceInfoList.stream()
            .map(OrderPriceInfo::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            refundOrderPriceInfosRetry(ids);
        }
    }

    @SneakyThrows
    public void refundOrderPriceInfosRetry(List<Integer> refundIds) {
        Map<String, RefundRequest> refundRequestMap = new HashMap<>();
        for (OrderPriceInfo refundOrderPriceInfo : priceInfoService.getRefundOrderPriceInfoListByIds(refundIds)) {
            // 是否曾退款成功
            if (refundOrderPriceInfo.getPaymentId() != null) {
                throw new SubscribeException(ALREADY_REFUND);
            }
            String key = refundOrderPriceInfo.getOrderNo() + refundOrderPriceInfo.getRecTradeId();
            RefundRequest refundRequest = refundRequestMap.get(key);

            if (refundRequest == null) {
                refundRequest = new RefundRequest();
                refundRequest.setOrderNo(refundOrderPriceInfo.getOrderNo());
                refundRequest.setAdditionalDataObject(new AdditionalData(new ArrayList<>()));
                refundRequest.setAmount(refundOrderPriceInfo.getAmount());
                if (StringUtils.isBlank(refundOrderPriceInfo.getRecTradeId())) {
                    refundRequest.setTradeId(priceInfoService.get(refundOrderPriceInfo.getRefPriceInfoNo()).getRecTradeId());
                } else {
                    refundRequest.setTradeId(refundOrderPriceInfo.getRecTradeId());
                }

            } else {
                refundRequest.setAmount(refundRequest.getAmount() + refundOrderPriceInfo.getAmount());
            }
            refundRequest.getAdditionalDataObject().getOrderPriceInfoIds().add(refundOrderPriceInfo.getId());
            refundRequestMap.put(key, refundRequest);

        }
        for (RefundRequest refundRequest : refundRequestMap.values()) {
            refundRequest.setAdditionalData(objectMapper.writeValueAsString(refundRequest.getAdditionalDataObject()));
            asyncRefund(refundRequest);
        }
    }

    /**
     * 非同步退款
     */
    public void asyncRefund(@NonNull RefundRequest refundRequest) {

        CompletableFuture.supplyAsync(() -> paymentServer.refund(refundRequest), executor)
            .whenComplete((result, e) -> {
                if (0 != result.getStatusCode() || null != e) {
                    log.error("訂單: {}, Payment 退款失敗 tradeId: {}", refundRequest.getOrderNo(), refundRequest.getTradeId());
                    mattermostServer.notify("Refund 失敗", new SingletonMap<>("msg", String.format("訂單: %s, Payment 退款失敗 tradeId: %s", refundRequest.getOrderNo(), refundRequest.getTradeId())), e);
                    return;
                }
                log.info("訂單: {}, Payment 退款成功 tradeId: {}", refundRequest.getOrderNo(), refundRequest.getTradeId());
            });
    }

    /**
     * 退款請求驗證
     */
    private void refundRequestValidate(OrderPriceInfoRefundRequest orderPriceInfoRefundRequest) {
        for (OrderPriceInfoRefundRequest.OrderPriceInfoRefund refund : orderPriceInfoRefundRequest.getRefunds()) {
            if (!priceInfoService.orderPriceInfoCanRefund(refund.getOrderPriceInfoId(), refund.getRefundAmount())) {
                throw new SubscribeException(REFUND_AMOUNT_OVER_PAY_AMOUNT);
            }
        }
    }

    /**
     * 收到保證金Queue處理
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void consumeSecurityDepositPayment(MainContract mainContract, PaymentQueue paymentQueue) throws JsonProcessingException {
        if (paymentQueue.getAmount() <= 0) {
            log.warn("金額小於等於0，待查驗{}", paymentQueue);
        }
        if (paymentQueue.getPaymentCategory() == PayAuth) {
            // 只針對預授權做事(0)，實際請款不做事
            if (OrderPaymentStatus.AUTHORIZED.getCode() == paymentQueue.getStatus()) {
                Orders orders = orderService.getOrder(paymentQueue.getOrderId());
                SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();
                securityDepositInfo.getSecurityDepositPaid(paymentQueue.getAmount());
                securityDepositInfo.setSecurityDepositDate(new Date());
                // 訂單改為已訂車(10)
                orderService.bookingOrder(orders);

                priceInfoService.receivePayment(paymentQueue);
                contractService.updateMainContract(mainContract);
                // 保證金立賬
                checkoutService.securityDepositCheckOut(orders, paymentQueue, false);

                // 寄送通知
                AuthUser user = authServer.getUserWithRetry(mainContract.getAcctId());
                notifyService.notifyPaySecurityDepositSuccess(orders, user);
                notifyToCService.notifySecurityDepositPaid(orders, user);
                carsService.updateLocationStationBasedOnSgType(mainContract);
                crsService.subscribeCarControl(carsService.findByPlateNo(mainContract.getPlateNo()));
            } else if (paymentQueue.getStatus() == OrderPaymentStatus.CANCEL_3D.getCode()
                || paymentQueue.getStatus() == OrderPaymentStatus.FAIL_3D.getCode()
                || paymentQueue.getStatus() == OrderPaymentStatus.TRANSACTION_ERROR.getCode()
                || paymentQueue.getStatus() == OrderPaymentStatus.PENDING_PAYMENT.getCode()) {
                Orders order = orderService.getOrder(paymentQueue.getOrderId());
                if ((order.getStatus() < OrderStatus.BOOKING.getStatus() || order.getStatus().equals(OrderStatus.CANCEL.getStatus())) && order.getIsNewOrder()) {
                    AccountListResponse response = null;
                    try {
                        response = paymentServer.accountList(order.getOrderNo());
                    } catch (Exception ignore) {
                        // ignore
                    }

                    if (response != null && response.getStatusCode() == 0) {
                        com.carplus.subscribe.model.payment.AccountDetail accountDetail = Optional.ofNullable(response.getData()).map(AccountList::getAccountDetails).orElseGet(Lists::newArrayList)
                            .stream()
                            .filter(account -> account.getPaymentId().equals(paymentQueue.getPaymentId())).findAny().orElseThrow(() -> new ServerException("無法取得tappay交易紀錄"));

                        if (paymentQueue.getTradeId().equals(accountDetail.getRecTradeId())) {
                            Cars car = carsService.findByPlateNo(order.getPlateNo());
                            boolean isProcessOrder = car != null && CollectionUtils.isNotEmpty(orderService.getProcessOrdersByPlateNos(Collections.singletonList(car.getPlateNo())));
                            if (car != null && CarDefine.CarStatus.Subscribed.getCode().equals(car.getCarStatus()) && !isProcessOrder) {
                                carsService.updateStatus(car, CarDefine.CarStatus.Free);
                            }
                        }
                    }

                }

            }
        } else if (paymentQueue.getPaymentCategory() == PaymentCategory.Refund) {
            SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();
            if (OrderPaymentStatus.getPendingRefundCode().contains(paymentQueue.getStatus())) {
                int oriSecurityRefundDepositAmt = securityDepositInfo.getRefundSecurityDeposit();
                priceInfoService.receiveRefund(paymentQueue);
                securityDepositInfo.requestRefundSecurityDeposit(new Date(), paymentQueue.getAmount());
                contractService.updateMainContract(mainContract);
                // 保證金立賬
                if ((paymentQueue.getStatus() == OrderPaymentStatus.AUTHORIZED.getCode()
                    || (paymentQueue.getStatus() == OrderPaymentStatus.PENDING_REFUND_FOR_BANK_CAPTURE.getCode() && oriSecurityRefundDepositAmt == 0
                    && StringUtils.isBlank(paymentQueue.getBankResultMsg())
                    && (StringUtils.isBlank(paymentQueue.getBankResultCode()) || paymentQueue.getBankResultCode().equals("00"))))
                    && mainContract.getContracts().get(0).getOrders().get(0).getStatus() != OrderStatus.CANCEL.getStatus()) {
                    Orders orders = orderService.getOrder(paymentQueue.getOrderId());
                    checkoutService.securityDepositCheckOut(orders, paymentQueue, false);
                }
            } else if (OrderPaymentStatus.getActualRefundCode().contains(paymentQueue.getStatus())) {
                securityDepositInfo.refundSecurityDeposit(paymentQueue.getPaymentDealDate());
                contractService.updateMainContract(mainContract);
                // 保證金退款通知客戶

                AuthUser user = authServer.getUserWithRetry(mainContract.getAcctId());
                notifyToCService.notifyRefund(mainContract.getContracts().get(0).getOrders().get(0), user, paymentQueue.getAmount());


            } else if (OrderPaymentStatus.EXPIRED_REFUND.getCode() == paymentQueue.getStatus()) {
                AuthUser user = authServer.getUserWithRetry(mainContract.getAcctId());
                PaymentInfo securityDepositPay =
                    getPaymentInfosByTradeId(paymentQueue.getTradeId()).stream().filter(p -> Objects.equals(p.getPayFor(), SecurityDeposit) && p.getPaymentCategory().equals(PayAuth)).findAny().orElse(null);
                TradeHistoryResp tradeHistoryResp = paymentServer.getTappayHistory(securityDepositPay.getTradeId());
                Date transactionTime =
                    Optional.ofNullable(tradeHistoryResp).map(TradeHistoryResp::getData).orElse(new ArrayList<>()).stream().filter(tradeHistory -> tradeHistory.getAction() == 0).findAny().map(TradeHistory::getMillis).map(Date::new)
                        .orElse(new Date(securityDepositPay.getInstantCreateDate().toEpochMilli()));
                notifyService.notifyManualRefundSecurityDeposit(orderService.getOrder(paymentQueue.getOrderId()), user, paymentQueue, securityDepositPay, transactionTime);
            }
        }
    }

    /**
     * 收到收/退款Queue處理
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void consumePayment(PaymentQueue paymentQueue) throws JsonProcessingException {
        if (paymentQueue.getAmount() <= 0) {
            log.warn("金額小於等於0，待查驗{}", paymentQueue);
        }
        if (paymentQueue.getPaymentCategory() == PayAuth) {
            // 只針對預授權做事(0)，實際請款不做事
            if (OrderPaymentStatus.AUTHORIZED.getCode() == paymentQueue.getStatus()) {
                Orders orders = orderService.getOrder(paymentQueue.getOrderId());
                checkoutService.advance(paymentQueue, orders);
                priceInfoService.receivePayment(paymentQueue);
            }
        } else if (paymentQueue.getPaymentCategory() == PaymentCategory.Refund) {
            if (OrderPaymentStatus.getPendingRefundCode().contains(paymentQueue.getStatus())) {
                priceInfoService.receiveRefund(paymentQueue);
            } else if (OrderPaymentStatus.getActualRefundCode().contains(paymentQueue.getStatus())) {
                Orders order = orderService.getOrder(paymentQueue.getOrderId());
                AuthUser user = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
                notifyToCService.notifyRefund(order, user, paymentQueue.getAmount());
            }
        }
    }

    @Transactional
    public void afterPay(PaymentQueue paymentQueue) throws JsonProcessingException {
        if (paymentQueue.getPaymentCategory() == PayAuth) {
            // 只針對預授權做事(0)，實際請款不做事
            if (OrderPaymentStatus.AUTHORIZED.getCode() == paymentQueue.getStatus()) {
                List<Integer> ids = objectMapper.readValue(paymentQueue.getAdditionalData(), AdditionalData.class).getOrderPriceInfoIds();
                List<OrderPriceInfo> orderPriceInfos = orderPriceInfoRepository.findAllById(ids);
                priceInfoService.afterPay(paymentQueue, orderService, mattermostServer, orderPriceInfos);
            }
        }
    }

    /**
     * 儲存收到的 Payment Queue
     */
    public void savePaymentQueue(PaymentQueue queue) {
        PaymentInfo paymentInfo = new PaymentInfo();
        BeanUtils.copyProperties(queue, paymentInfo);
        if (queue.getAcquirer() == Acquirer.TW_TAISHIN) {
            paymentInfo.setChargeType(ChargeType.Tappay_TAISHIN.getCreditBankAuto());
        } else if (queue.getAcquirer() == Acquirer.TW_CTBC) {
            paymentInfo.setChargeType(ChargeType.Tappay_CTBC.getCreditBankAuto());
        } else if (queue.getAcquirer() == Acquirer.TW_NCCC) {
            paymentInfo.setChargeType(ChargeType.Tappay_NCCC.getCreditBankAuto());
        }

        if (queue.getPaymentType() == PaymentType.CreditCard) {
            paymentInfo.setAccountType(Credit);
        }
        paymentInfoRepository.save(paymentInfo);
    }

    /**
     * 是否為線上付款
     */
    public boolean isPaidOnline(@NonNull AccountRecord account) {
        if (account.getAccountType() != Credit) {
            return false;
        }

        // 14: 中信銀Tappay, 15: 台新銀Tappay , 16:聯信Tappay
        return account.getChargeType() != null
            && (account.getChargeType() == ChargeType.Tappay_CTBC.getCreditBankAuto()
            || account.getChargeType() == ChargeType.Tappay_TAISHIN.getCreditBankAuto()
            || account.getChargeType() == ChargeType.Tappay_NCCC.getCreditBankAuto());
    }

    /**
     * 取得訂單已經收支登打資料
     */
    public List<Account> getAccountsByOrders(String orderNo) {
        return accountRepository.getAccountsByOrderNo(orderNo);
    }

    /**
     * 取得訂單的收支登打資料
     */
    public List<Account> getAccountsByOrder(String orderNo) {
        // 檢查是否有該訂單
        orderService.getOrder(orderNo);
        List<Account> accountList = accountRepository.getAccountsByOrderNo(orderNo);
        Map<String, Account> accountMap = accountList.stream().filter(account -> !account.isDeleted() && account.getAccountType() == Credit).collect(Collectors.toMap(Account::getTradeId, account -> account));
        List<PaymentInfo> paymentInfoList = getPaymentsByOrder(orderNo).stream()
            .filter(paymentInfo -> PayAuth.equals(paymentInfo.getPaymentCategory())
                && !paymentInfo.getPayFor().equals(SecurityDeposit)).collect(Collectors.toList());
        List<OrderPriceInfo> priceInfoList = priceInfoService.getPriceInfoWrapper(orderNo).getList();
        List<Integer> paymentIds = priceInfoList.stream().map(OrderPriceInfo::getPaymentId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, Map<Integer, Integer>> mapRemitAccIdToPriceInfoIds = priceInfoList.stream()
            .filter(pi -> CollectionUtils.isNotEmpty(pi.getRemitAccountIds())
                && pi.getRemitAccountIds().stream().noneMatch(Objects::isNull))
            .flatMap(pi -> pi.getRemitAccountIds().stream()
                .map(accId -> {
                    Map<Long, Map<Integer, Integer>> map = new HashMap<>();
                    map.put(accId, new SingletonMap<>(pi.getId(), pi.getActualPrice()));
                    return map;
                })).findAny().orElseGet(HashMap::new);
        for (PaymentInfo paymentInfo : paymentInfoList) {
            Account acc = accountMap.get(paymentInfo.getTradeId());
            if (!paymentIds.contains(paymentInfo.getPaymentId())) {
                continue;
            }
            Integer negative = calculateNegative(priceInfoList, paymentInfo).get();
            if (acc != null) {
                acc.setAmount(acc.getAmount());
                acc.setTotalRefundAmount(acc.getTotalRefundAmount());
                acc.setRefundAmount(negative - acc.getTotalRefundAmount());
            } else {
                // 濾過單筆Etag付款
                int etagAmt = getEtagAmount(priceInfoList, paymentInfo);
                if (etagAmt != 0 && (etagAmt == paymentInfo.getAmount() || etagAmt == paymentInfo.getAmount() - negative)) {
                    continue;
                }
                acc = new Account();
                acc.setAmount(paymentInfo.getAmount() - etagAmt);
                acc.setTotalRefundAmount(0);
                acc.setRefundAmount(negative);
                acc.setOrderNo(orderNo);
                acc.setOriginalAmount(paymentInfo.getAmount());
                acc.setAccountType(paymentInfo.getAccountType());
                acc.setStationCode(subscribeStationCode);
                acc.setTradeId(paymentInfo.getTradeId());
                acc.setChargeType(paymentInfo.getChargeType());
                acc.setCardNumber(paymentInfo.getCardNumber());
                acc.setAuthCode(paymentInfo.getAuthCode());
                acc.setPayFor(paymentInfo.getPayFor());
                acc.setTransactionNumber(paymentInfo.getTransactionNumber());
                accountList.add(acc);
            }
        }
        calculateRefundRemitAccount(accountList, mapRemitAccIdToPriceInfoIds, priceInfoList);
        offsetRefundAmountsWithDeletedAccounts(accountList);
        // 過濾匯款金額為0的資料
        accountList = accountList.stream().filter(account -> !(account.getAccountType() == Remit && account.getAmount() == 0)).collect(Collectors.toList());
        return accountList;
    }

    private void calculateRefundRemitAccount(List<Account> accountList, Map<Long, Map<Integer, Integer>> mapRemitAccIdToPriceInfoIds, List<OrderPriceInfo> priceInfoList) {
        Map<Long, Account> accountMap = accountList.stream().filter(acc -> acc.getAccountType() == Remit).collect(Collectors.toMap(Account::getId, Function.identity()));
        accountMap.forEach((key, value) -> value.setOrderPriceAmounts(mapRemitAccIdToPriceInfoIds.get(key)));
        priceInfoList.stream().filter(opi -> CollectionUtils.isNotEmpty(opi.getRemitAccountIds())).forEach(opi -> {
            if (opi.getType() == Refund.getCode()) {
                AtomicInteger refundAmt = new AtomicInteger(opi.getAmount() - opi.getReceivedAmount());
                opi.getRemitAccountIds().forEach(accId -> {
                    Account account = accountMap.get(accId);
                    if (refundAmt.get() > 0) {
                        account.setRefundAmount(refundAmt.get());
                        if (account.getRefundAmount() > account.getAmount()) {
                            refundAmt.set(account.getRefundAmount() - account.getAmount());
                            account.setRefundAmount(account.getAmount());
                        } else {
                            refundAmt.set(0);
                        }
                    } else {
                        account.setRefundAmount(0);
                    }
                    account.setOrderPriceAmounts(mapRemitAccIdToPriceInfoIds.get(account.getId()));
                });
            }
        });
    }

    private void offsetRefundAmountsWithDeletedAccounts(List<Account> accountList) {
        List<Account> activeAccounts = accountList.stream()
            .filter(acc -> !acc.isDeleted())
            .collect(Collectors.toList());

        int totalDeletedRefundAmount = accountList.stream()
            .filter(Account::isDeleted)
            .collect(Collectors.toList()).stream()
            .mapToInt(Account::getTotalRefundAmount)
            .sum();

        for (Account activeAccount : activeAccounts) {
            int initialRefundAmount = Optional.ofNullable(activeAccount.getRefundAmount()).orElse(0);
            int offsetAmount = Math.min(totalDeletedRefundAmount, initialRefundAmount);
            activeAccount.setRefundAmount(initialRefundAmount - offsetAmount);
            totalDeletedRefundAmount -= offsetAmount;

            if (totalDeletedRefundAmount <= 0) {
                break;
            }
        }
    }

    /**
     * 取得Etag費用
     */
    private int getEtagAmount(List<OrderPriceInfo> priceInfoList, PaymentInfo paymentInfo) {

        return priceInfoList.stream().filter(orderPriceInfo -> Objects.equals(orderPriceInfo.getPaymentId(), paymentInfo.getPaymentId()) && orderPriceInfo.getCategory().equals(ETag))
            .mapToInt(p -> (p.getType() == 0 ? 1 : -1) * p.getReceivedAmount()).sum();
    }

    private AtomicInteger calculateNegative(List<OrderPriceInfo> priceInfoList, PaymentInfo paymentInfo) {
        AtomicInteger negative = new AtomicInteger();
        List<OrderPriceInfo> existList = priceInfoList.stream().filter(orderPriceInfo -> Objects.equals(orderPriceInfo.getPaymentId(), paymentInfo.getPaymentId())).collect(Collectors.toList());
        existList.forEach(orderPriceInfo -> {
            priceInfoService.getRefOrderPriceInfoListByPayId(orderPriceInfo.getId()).forEach(refOrderPriceInfo -> {
                if (refOrderPriceInfo.getType() == PriceInfoDefinition.PriceInfoType.Refund.getCode()) {
                    if (refOrderPriceInfo.getInfoDetail() == null || Objects.equals(refOrderPriceInfo.getInfoDetail().getIsAgree(), Boolean.TRUE)) {
                        negative.addAndGet(refOrderPriceInfo.getAmount());
                    }
                }
            });
        });
        return negative;
    }

    private AtomicInteger calculateNegative(List<OrderPriceInfo> priceInfoList, Long remitNo, Map<Long, Integer> refundMap) {
        AtomicInteger negative = new AtomicInteger();
        List<OrderPriceInfo> existList = priceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getRemitAccountIds() != null
            && !orderPriceInfo.getRemitAccountIds().isEmpty()
            && orderPriceInfo.getRemitAccountIds().contains(remitNo)).collect(Collectors.toList());
        existList.forEach(orderPriceInfo -> {
            priceInfoService.getRefOrderPriceInfoListByPayId(orderPriceInfo.getId()).forEach(refOrderPriceInfo -> {
                if (refOrderPriceInfo.getType() == PriceInfoDefinition.PriceInfoType.Refund.getCode()) {
                    if (refOrderPriceInfo.getInfoDetail() == null || Objects.equals(refOrderPriceInfo.getInfoDetail().getIsAgree(), Boolean.TRUE)) {
                        negative.addAndGet(refOrderPriceInfo.getAmount());
                        // 因為可能多筆匯款為一筆，故須處理重複計算問題
                        for (Long remitId : orderPriceInfo.getRemitAccountIds()) {
                            if (refundMap.get(remitId) != null) {
                                negative.addAndGet(-1 * refundMap.get(remitId));
                            }
                        }

                    }
                }
            });
        });
        if (negative.get() < 0) {
            negative.set(0);
        }
        return negative;
    }

    /**
     * 檢查登打與收支是否平衡
     */
    public boolean checkBalance(String orderNo) {
        // 檢查是否有該訂單
        Orders order = orderService.getOrder(orderNo);
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        int receiveAmt = priceInfoWrapper.getCurrentReceivable().getActualPrice();
        //因還車後才進行結算退款，顧不依據paymentInfo作為是否收支平衡之依據
//        int paymentTotalAmt = paymentInfoRepository.getPaymentInfosByOrderNo(orderNo).stream().filter(paymentInfo -> paymentInfo.getPayFor() != PayFor.SecurityDeposit)
//            .mapToInt(paymentInfo -> (PaymentCategory.Refund == paymentInfo.getPaymentCategory() ? -1 : 1) * paymentInfo.getAmount()).sum();
        int accountAmt = accountRepository.getAccountAmtByOrderNo(orderNo);
        log.info("應收:{}, 實收:{}", receiveAmt, accountAmt);
        return accountAmt == receiveAmt;
    }

    /**
     * 檢查登打與收支是否平衡
     */
    public boolean checkBalance(Orders order, List<AccountRecord> accountRecords, Map<Long, Account> dbAccounts) {
        // db accounts 已經有的金額
        int total = calculateTotal(accountRecords, dbAccounts);

        log.info("訂單: {}, 加總應收退總金額: {}", order.getOrderNo(), total);

        int receiveAmt = calculateReceivableAmount(order);
        log.info("訂單: {}, 應收總額: {}", order.getOrderNo(), receiveAmt);
        if (receiveAmt != total) {
            throw new BadRequestException("加總應收退款金額不等於應收總額");
        }
        return true;
    }

    private int calculateTotal(List<AccountRecord> accountRecords, Map<Long, Account> dbAccounts) {
        int total = dbAccounts.values()
            .stream()
            .mapToInt(Account::getAmount)
            .sum();

        for (AccountRecord reqAccount : accountRecords) {
            Optional<Account> dbAccount = Optional.ofNullable(reqAccount.getId()).map(dbAccounts::get);
            if (dbAccount.isPresent()) {
                // 減去異動前
                total -= dbAccount.get().getAmount();
            }

            if (!reqAccount.isDeleted()) {
                // id != null 加原金額、減原退款金額
                // id == null 加收款金額、減退款金額
                total += reqAccount.getOriginalAmount();
                total -= reqAccount.getTotalRefundAmount();
                total -= reqAccount.getRefundAmount();
            }
        }
        return total;
    }

    private int calculateReceivableAmount(Orders order) {
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(order.getOrderNo());
        int receiveAmt = priceInfoWrapper.getCurrentReceivable().getActualPrice();
        if (order.getStatus() == OrderStatus.CANCEL.getStatus()) {
            receiveAmt += priceInfoWrapper.getByCategory(PriceInfoDefinition.PriceInfoCategory.SecurityDeposit).getActualReceivePrice();
        }
        return receiveAmt;
    }

    /**
     * 登打收支
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public PaymentRes recordAccounts(PaymentRequest paymentRequest, String orderNo) {
        Orders order = orderService.getOrder(orderNo);
        // 訂閱訂單收支登打條件
        if (order.getStatus() < OrderStatus.BOOKING.getStatus()) {
            throw new BadRequestException("訂閱訂單未成立，不可登打收支");
        }
        remitMaxAmountValidate(paymentRequest.getAccountRecords(), order.getCreateDate());
        List<Account> accounts = updateRecordAccounts(order, paymentRequest.getAccountRecords());

        return new PaymentRes(order, accounts.stream().filter(account -> !account.isDeleted()).sorted(Comparator.comparing(Account::getId)).collect(Collectors.toList()));
    }

    /**
     * 更新收支登打
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<Account> updateRecordAccounts(Orders order, List<AccountRecord> accountRecords) {
        accountRecords = accountRecords.stream()
            .filter(account -> order.getStatus() == OrderStatus.CANCEL.getStatus() || account.getPayFor() != SecurityDeposit)
            .collect(Collectors.toList());

        Map<Long, Account> dbAccounts = getAccountsByOrders(order.getOrderNo()).stream()
            .filter(account -> !account.isDeleted()).collect(Collectors.toMap(Account::getId, Function.identity()));
        checkBalance(order, accountRecords, dbAccounts);

        updateAccountsAndDetails(order, accountRecords, dbAccounts);

        updateRemitAccount(order, new ArrayList<>(dbAccounts.values()), accountRecords);
        // 匯款退款金額設定
        setRemitReceiveAmount(order.getOrderNo(), accountRecords, priceInfoService.getPriceInfoWrapper(order.getOrderNo()));
        // 發動退款
        refundByAccountRequest(order, accountRecords);
        // 等待Queue回來
        checkTappayRefundSuccess(order.getOrderNo(), accountRecords);
        return new ArrayList<>(dbAccounts.values());
    }

    private void updateAccountsAndDetails(Orders order, List<AccountRecord> accountRecords, Map<Long, Account> dbAccounts) {
        if (!accountRecords.isEmpty()) {
            // account table 寫入
            List<Account> dbNewAccounts = getNewAccounts(order, accountRecords, dbAccounts);
            accountRepository.saveAll(dbNewAccounts);
            dbAccounts.putAll(dbNewAccounts.stream().collect(Collectors.toMap(Account::getId, Function.identity())));
            accountRepository.saveAll(dbAccounts.values());
            // accountDetail table 寫入
            List<AccountDetail> newAccountDetails = getNewAccountDetails(accountRecords, dbNewAccounts);
            accountDetailRepository.saveAll(newAccountDetails);
        }
    }

    /**
     * 更新匯款登打
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateRemitAccount(Orders order, List<Account> accounts, List<AccountRecord> accountRecords) {
        Map<Boolean, List<Account>> accountMap = accounts.stream().filter(account -> account.getAccountType() == AccountType.Remit).collect(Collectors.groupingBy(Account::isDeleted));
        List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());

        // Map accounts id to accountRecords id by same remitAccCode, remitNo, originalAmount if the id of accountRecord is null
        accountRecords.stream()
            .filter(ar -> ar.getId() == null && ar.getAccountType() == Remit && ar.getRemitAccCode() != null && ar.getRemitNo() != null && ar.getOriginalAmount() != null)
            .forEach(ar -> accountMap.get(false).stream()
                .filter(account -> account.getRemitAccCode().equals(ar.getRemitAccCode()) && account.getRemitNo().equals(ar.getRemitNo())
                    && Integer.valueOf(account.getAmount() + Optional.ofNullable(account.getRefundAmount()).orElse(0)).equals(ar.getOriginalAmount()))
                .findFirst().ifPresent(account -> ar.setId(account.getId())));

        Map<Integer, List<Long>> mapOrderPriceInfoIdToAccountId = new HashMap<>();
        accountRecords.stream()
            .filter(ar -> !ar.isDeleted() && ar.getAccountType() == AccountType.Remit && accountIds.contains(ar.getId()))
            .forEach(ar -> Optional.ofNullable(ar.getOrderPriceInfoAmounts())
                .orElse(Collections.emptyMap())
                .forEach((priceInfoId, amount) -> {
                    List<Long> accIds = Optional.ofNullable(mapOrderPriceInfoIdToAccountId.get(priceInfoId)).orElseGet(ArrayList::new);
                    accIds.add(ar.getId());
                    mapOrderPriceInfoIdToAccountId.put(priceInfoId, accIds);
                }));

        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfoWrapper(order.getOrderNo()).getList();

        // 處理刪除的登打
        for (Account deleteAccount : Optional.ofNullable(accountMap.get(true)).orElseGet(Collections::emptyList)) {
            for (OrderPriceInfo orderPriceInfo : orderPriceInfoList) {
                if (orderPriceInfo.getRemitAccountIds() != null && orderPriceInfo.getRemitAccountIds().contains(deleteAccount.getId()) && orderPriceInfo.getPaymentId() == null) {
                    orderPriceInfo.setReceivedAmount(0);
                    orderPriceInfo.setRemitAccountIds(null);
                }
            }
        }

        processEtagfInfo(orderPriceInfoList, mapOrderPriceInfoIdToAccountId);
        // 將匯款寫入OrderPriceInfo
        processOrderPriceInfo(accountMap, orderPriceInfoList, mapOrderPriceInfoIdToAccountId);
        orderPriceInfoRepository.saveAll(orderPriceInfoList);
    }

    @Transactional
    public void processEtagfInfo(List<OrderPriceInfo> orderPriceInfoList, Map<Integer, List<Long>> mapOrderPriceInfoIdToAccountId) {
        orderPriceInfoList.stream().filter(orderPriceInfo -> ETag.equals(orderPriceInfo.getCategory())
            && mapOrderPriceInfoIdToAccountId.containsKey(orderPriceInfo.getId())
            && !orderPriceInfo.isPaid()).forEach(etgPriceInfo -> eTagService.processETagRemit(etgPriceInfo));
    }


    public void processOrderPriceInfo(Map<Boolean, List<Account>> accountMap,
                                      List<OrderPriceInfo> orderPriceInfoList,
                                      Map<Integer, List<Long>> mapOrderPriceInfoIdToAccountId) {
        List<Account> accounts = accountMap.getOrDefault(false, Collections.emptyList());

        for (Account account : accounts) {
            for (OrderPriceInfo orderPriceInfo : orderPriceInfoList) {
                if (shouldProcessOrderPriceInfo(orderPriceInfo, account, mapOrderPriceInfoIdToAccountId)) {
                    processValidOrderPriceInfo(orderPriceInfo, mapOrderPriceInfoIdToAccountId);
                }
            }
        }
    }

    private boolean shouldProcessOrderPriceInfo(OrderPriceInfo orderPriceInfo, Account account,
                                                Map<Integer, List<Long>> mapOrderPriceInfoIdToAccountId) {
        List<Long> priceInfoRemitAccountIds = orderPriceInfo.getRemitAccountIds();
        boolean hasNoRemitAccounts = priceInfoRemitAccountIds == null || priceInfoRemitAccountIds.isEmpty()
            || (priceInfoRemitAccountIds.size() == 1 && priceInfoRemitAccountIds.get(0) == null);
        boolean hasNoPaymentId = orderPriceInfo.getPaymentId() == null;
        boolean isNotOnlineRefund = !(orderPriceInfo.getType() == PriceInfoDefinition.PriceInfoType.Refund.getCode()
            && orderPriceInfo.getRefPriceInfoNo() != null);
        boolean isAssociatedWithAccount = mapOrderPriceInfoIdToAccountId.containsKey(orderPriceInfo.getId())
            && mapOrderPriceInfoIdToAccountId.get(orderPriceInfo.getId()).contains(account.getId());

        return hasNoRemitAccounts && hasNoPaymentId && isNotOnlineRefund && isAssociatedWithAccount;
    }

    private void processValidOrderPriceInfo(OrderPriceInfo orderPriceInfo,
                                            Map<Integer, List<Long>> mapOrderPriceInfoIdToAccountId) {
        if (isMileageFeeWithMissingInfo(orderPriceInfo)) {
            return;
        }
        orderPriceInfo.setReceivedAmount(orderPriceInfo.getAmount());
        orderPriceInfo.setRemitAccountIds(mapOrderPriceInfoIdToAccountId.get(orderPriceInfo.getId()));
    }

    private boolean isMileageFeeWithMissingInfo(OrderPriceInfo orderPriceInfo) {
        return MileageFee.equals(orderPriceInfo.getCategory())
            && orderPriceInfo.getType() == Pay.getCode()
            && (orderPriceInfo.getInfoDetail() == null
            || orderPriceInfo.getInfoDetail().getEndMileage() == null
            || orderPriceInfo.getAmount() == 0);
    }

    @NonNull
    private List<Account> getNewAccounts(
        @NonNull Orders orders,
        @NonNull List<AccountRecord> reqAccounts,
        @NonNull Map<Long, Account> dbAccounts
    ) {
        List<Account> newAccounts = new ArrayList<>();

        for (AccountRecord reqAccount : reqAccounts) {
            int totalRefundAmount = reqAccount.getRefundAmount() + reqAccount.getTotalRefundAmount();
            int amount = (reqAccount.getOriginalAmount() - totalRefundAmount);
            Map<Integer, Integer> orderPriceInfoAmounts = Optional.ofNullable(reqAccount.getOrderPriceInfoAmounts()).orElseGet(HashMap::new);
            if (orderPriceInfoAmounts.isEmpty() && StringUtils.isNotBlank(reqAccount.getTradeId())) {
                orderPriceInfoAmounts = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().recTradeId(reqAccount.getTradeId()).build()).stream().collect(Collectors.toMap(OrderPriceInfo::getId, OrderPriceInfo::getActualPrice));
                reqAccount.setOrderPriceInfoAmounts(orderPriceInfoAmounts);
            }
            if (reqAccount.getId() == null) {
                // 建立 account 資料
                log.info("訂單: {}, 新增 account 收款: {}, 退款: {}", orders.getOrderNo(), reqAccount.getOriginalAmount(), reqAccount.getRefundAmount());
                newAccounts.add(new Account(reqAccount, orders.getOrderNo(), amount, totalRefundAmount, new ArrayList<>(orderPriceInfoAmounts.keySet()), orderPriceInfoAmounts));
            } else {
                Account dbAccount = Optional.ofNullable(dbAccounts.get(reqAccount.getId())).orElseThrow(() -> new BadRequestException("取得 account 資料失敗, id: " + reqAccount.getId()));
                if (reqAccount.isDeleted()) {
                    log.info("訂單: {}, 刪除 account id: {}", orders.getOrderNo(), reqAccount.getId());
                    dbAccount.setDeleted(true);
                } else {
                    // 登打過不可能有收入
                    reqAccount.setOrderPriceInfoAmounts(orderPriceInfoAmounts.entrySet().stream().filter(entry -> entry.getValue() < 0).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
                    // 更新 account 金額
                    dbAccount.setAmount(amount);
                    // 更新 account 總退款金額
                    dbAccount.setTotalRefundAmount(totalRefundAmount);
                }
            }
        }

        return newAccounts;
    }

    @NonNull
    private List<AccountDetail> getNewAccountDetails(
        @NonNull List<AccountRecord> reqAccounts,
        @NonNull List<Account> dbNewAccounts
    ) {

        List<AccountRecord> modifiedAccounts = reqAccounts
            .stream()
            .filter(a -> null != a.getId())
            .collect(Collectors.toList());

        List<Long> modifiedAccountIds = modifiedAccounts.stream().map(AccountRecord::getId).collect(Collectors.toList());
        // 取得已寫入 accountDetail 的總和 by accountId
        Map<Integer, Integer> accountIdSum = accountDetailRepository.findSumByAccountIdIn(modifiedAccountIds);
        Map<Long, List<AccountDetail>> accountIdToOrderPriceInfoIds = accountDetailRepository.getDetailByAccountIds(modifiedAccountIds);


        List<AccountDetail> newAccountDetails = new ArrayList<>();

        // 異動 account
        for (AccountRecord reqAccount : modifiedAccounts) {
            int newAmount = reqAccount.getOriginalAmount() - reqAccount.getRefundAmount() - reqAccount.getTotalRefundAmount();
            Integer detailTotalAmount = accountIdSum.get(reqAccount.getId().intValue());
            if (null == detailTotalAmount) {
                continue;
            }

            int amount = newAmount - detailTotalAmount;
            if (0 == amount) {
                continue;
            }

            log.info("accountId: {}, modified accountDetail amount: {}", reqAccount.getId(), amount);
            Map<Integer, Integer> diffOrderPriceInfoIdAmounts = Optional.ofNullable(reqAccount.getOrderPriceInfoAmounts()).orElseGet(HashMap::new);
            if (MapUtils.isNotEmpty(reqAccount.getOrderPriceInfoAmounts())) {
                accountIdToOrderPriceInfoIds.get(reqAccount.getId()).stream().filter(detail -> MapUtils.isNotEmpty(detail.getOrderPriceAmounts())).forEach(detail -> {
                    detail.getOrderPriceAmounts().forEach((k, v) -> {
                        diffOrderPriceInfoIdAmounts.remove(k);
                    });
                });
            }
            newAccountDetails.add(new AccountDetail(reqAccount.getId(), amount, diffOrderPriceInfoIdAmounts));
        }

        // 新增 account
        for (Account dbAccount : dbNewAccounts) {
            // 收
            int newAmount = dbAccount.getTotalRefundAmount() + dbAccount.getAmount();
            if (0 == newAmount) {
                continue;
            }

            log.info("accountId: {}, new accountDetail amount: {}", dbAccount.getId(), newAmount);
            newAccountDetails.add(new AccountDetail(dbAccount.getId(), newAmount, dbAccount.getOrderPriceAmounts()));

            // 退
            int newRefundAmount = -1 * dbAccount.getTotalRefundAmount();
            if (0 == newRefundAmount) {
                continue;
            }

            log.info("accountId: {}, refund accountDetail amount: {}", dbAccount.getId(), newRefundAmount);
            newAccountDetails.add(new AccountDetail(dbAccount.getId(), newRefundAmount, dbAccount.getOrderPriceAmounts()));
        }

        return newAccountDetails;
    }

    /**
     * 取得保證金明細
     */
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public SecurityDepositInfoResponse getSecurityDepositInfo(String orderNo) {
        SecurityDepositInfoResponse response = new SecurityDepositInfoResponse();
        Orders order = orderService.getOrder(orderNo);
        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();

        // 保證金應收已收
        response.setRealSecurityDeposit(securityDepositInfo.getRealSecurityDeposit());
        response.setPaidSecurityDeposit(securityDepositInfo.getPaidSecurityDeposit());

        PaymentInfo query = new PaymentInfo();
        query.setOrderId(orderNo);
        query.setPayFor(SecurityDeposit);
        query.setPaymentCategory(PaymentCategory.Refund);
        Example example = Example.of(query);
        Optional optional = paymentInfoRepository.findOne(example);

        // 保證金退款交易狀態
        if (optional.isPresent()) {
            PaymentInfo info = (PaymentInfo) optional.get();
            response.setPaymentStatus(info.getStatus());
        } else {
            response.setPaymentStatus(null);
        }

        // 應退金額
        if (OrderStatus.CLOSE.getStatus() == order.getStatus()) {
            response.setRefundSecurityDeposit(securityDepositInfo.getRefundSecurityDeposit());
        } else {
            response.setRefundSecurityDeposit(0);
        }
        return response;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateApplyDate(String orderNo, Date applyDate, ManualRefundStatus manualRefundStatus, ManualRefundMethod manualRefundMethod, String headerMemberId) {
        Orders order = orderService.getOrder(orderNo);
        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();
        ManualRefundStatus currentStatus = securityDepositInfo.getManualRefundStatus();
        securityDepositInfo.setManualRefundStatus(manualRefundStatus);
        securityDepositInfo.manualRefundSecurityDeposit(applyDate, manualRefundMethod, headerMemberId);
        contractService.updateMainContract(mainContract);
        if ((currentStatus == null || currentStatus.equals(ManualRefundStatus.PENDING)) && manualRefundMethod == ManualRefundMethod.Credit) {
            checkoutService.manualRefundSecurityDepositCheckOut(order);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public Page<PaymentInfoQueryResponse> searchByPage(PageRequest pageable, PaymentInfoQueryRequest queryRequest) {
        int limit = pageable.getLimit();
        int offset = pageable.getSkip();

        Map<String, Stations> stationsMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, Function.identity()));
        List<PaymentInfoQueryResponse> list = searchPage(queryRequest, limit, offset, stationsMap);
        if (queryRequest.getManualRefundStatus() != null) {
            if (queryRequest.getManualRefundStatus()) {
                list = list.stream().filter(PaymentInfoQueryResponse::getIsManualRefundSecurityDeposit).collect(Collectors.toList());
            } else {
                list = list.stream().filter(res -> !res.getIsManualRefundSecurityDeposit()).collect(Collectors.toList());
            }
        }
        int total = list.size();
        // 分頁
        limit = Math.min(limit, list.size() - offset);
        int toIndex = offset + limit;
        list = list.subList(offset, toIndex);

        Map<String, Orders> ordersMap = orderService.getOrders(list.stream().map(PaymentInfoQueryResponse::getOrderNo).collect(Collectors.toList())).stream().collect(Collectors.toMap(Orders::getOrderNo, o -> o));
        List<AuthUser> authUsers = authServer.getUserAcctIds(list.stream().map(PaymentInfoQueryResponse::getAcctId).toArray(Integer[]::new));
        Map<Integer, String> authMap = authUsers.stream().collect(Collectors.toMap(AuthUser::getAcctId, AuthUser::getAcctName));
        list.forEach(re -> {
            re.setAcctName(authMap.get(re.getAcctId()));
            re.setManualRefundUpdater(Optional.ofNullable(ordersMap.get(re.getOrderNo()))
                .map(Orders::getContract)
                .map(Contract::getMainContract)
                .map(MainContract::getOriginalPriceInfo)
                .map(PriceInfo::getSecurityDepositInfo)
                .map(SecurityDepositInfo::getManualRefundUpdater)
                .orElse(null));
            re.setManualRefundUpdateDate(Optional.ofNullable(ordersMap.get(re.getOrderNo()))
                .map(Orders::getContract)
                .map(Contract::getMainContract)
                .map(MainContract::getOriginalPriceInfo)
                .map(PriceInfo::getSecurityDepositInfo)
                .map(SecurityDepositInfo::getManualRefundUpdateDate)
                .orElse(null));
        });

        return Page.of(total, list, offset, limit);
    }

    public CsvUtil.ByteArrayOutputStream2ByteBuffer getPaymentInfoCsv(PaymentInfoQueryRequest queryRequest) {
        List<PaymentInfoQueryResponse> list = searchByPage(new PageRequest(Integer.MAX_VALUE, 0), queryRequest).getList();
        list.forEach(p -> p.setAcctName(StringUtils.mask(p.getAcctName(), '*')));
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            list,
            new String[] {"主約編號", "訂單編號", "出車站點", "還車站點", "會員ID", "會員名稱", "主約建立日期", "訂單出車日",
                "訂單狀態", "帳務狀態", "收單銀行", "金額", "交易時間", "交易序號", "款項編號", "保證金退款日", "是否人工保證金退款", "人工退款狀態"},
            true,
            ',',
            out,
            Charset.forName("big5"),
            PaymentInfoQueryResponse.class
        );
        return out;
    }

    private List<PaymentInfoQueryResponse> searchPage(PaymentInfoQueryRequest queryRequest, Integer limit, Integer offset, Map<String, Stations> stationsMap) {
        return paymentInfoRepository.findBySearch(queryRequest, limit, offset)
            .stream()
            .map(t -> convertToQueryResponse(t, stationsMap))
            .collect(Collectors.toList());
    }

    private PaymentInfoQueryResponse convertToQueryResponse(Tuple t, Map<String, Stations> stationsMap) {
        PaymentInfoQueryResponse resp = new PaymentInfoQueryResponse();
        resp.setMainContractNo((String) t.get("mainContractNo"));
        resp.setOrderNo((String) t.get("orderNo"));
        if (t.get("departStationCode") != null) {
            resp.setDepartStation(Optional.ofNullable(stationsMap.get(t.get("departStationCode"))).map(Stations::getStationName).orElse(null));
        }
        if (t.get("returnStationCode") != null) {
            resp.setReturnStation(Optional.ofNullable(stationsMap.get(t.get("returnStationCode"))).map(Stations::getStationName).orElse(null));
        }
        if (t.get("acctId") != null) {
            resp.setAcctId((int) Optional.ofNullable(t.get("acctId")).orElse(0));
        }
        if (t.get("mainCreateDate") != null) {
            resp.setMainCreateDate(((Date) t.get("mainCreateDate")));
        }
        if (t.get("refundSecurityDepositDate") != null && !"null".equals(t.get("refundSecurityDepositDate"))) {
            resp.setRefundSecurityDepositDate(new Date(Long.valueOf((String) t.get("refundSecurityDepositDate"))));
        }
        if (t.get("isManualRefundSecurityDeposit") != null) {
            resp.setIsManualRefundSecurityDeposit(Boolean.parseBoolean((String) t.get("isManualRefundSecurityDeposit")));
        }
        if (t.get("orderStartDate") != null) {
            resp.setOrderStartDate(((Date) t.get("orderStartDate")));
        }
        resp.setOrderStatus((Short) t.get("orderStatus"));
        resp.setOrderStatusName(Optional.ofNullable((Short) t.get("orderStatus")).map(Short::intValue).map(OrderStatus::of).map(OrderStatus::getName).orElse(null));
        resp.setPaymentStatus((Short) t.get("paymentStatus"));
        resp.setAmount((Integer) t.get("amount"));
        resp.setPayDate(DateUtils.toDateString((Date) t.get("payDate"), "yyyy-MM-dd HH:mm:ss"));
        resp.setPaymentId((BigInteger) t.get("paymentId"));
        resp.setPaymentStatusName(OrderPaymentStatus.of(resp.getPaymentStatus().intValue()).getName());
        resp.setTransactionNumber((String) t.get("transactionNumber"));
        resp.setAcquirer((String) t.get("acquirer"));
        resp.setRemark((String) t.get("remark"));
        resp.setRemarker((String) t.get("remarker"));
        if (resp.getPaymentStatus() == OrderPaymentStatus.EXPIRED_REFUND.getCode()) {
            resp.setManualRefundStatus(t.get("manualRefundStatus") == null || Objects.equals("null", t.get("manualRefundStatus").toString().toLowerCase())
                ? ManualRefundStatus.PENDING :
                ManualRefundStatus.valueOf(t.get("manualRefundStatus").toString().replaceAll("\"", "")));
            resp.setRefundMethod(t.get("refundMethod") == null || Objects.equals("null", t.get("refundMethod").toString().toLowerCase())
                ? null :
                ManualRefundMethod.valueOf(t.get("refundMethod").toString().replaceAll("\"", "")));
        }
        return resp;
    }

    /**
     * 取得使用者付款資訊 #主要為前端詢問BU是否已經收到Queue
     */
    public PaymentInfo getUserOrderPayment(Integer acctId, String orderNo, String tradeId) {
        orderService.getUserOrder(orderNo, acctId);
        List<PaymentInfo> paymentInfoList = getPaymentInfosByTradeId(tradeId);
        if (!paymentInfoList.isEmpty() && paymentInfoList.get(0).getOrderId().equals(orderNo)) {
            return paymentInfoList.get(0);
        } else {
            return null;
        }
    }

    /**
     * 帳務登打並新增/異動發票
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public AccountSettlementResponse accountSettlement(AccountSettlementRequest request, String orderNo, String memberId) {
        AccountSettlementResponse response = new AccountSettlementResponse();
        // 先檢查發票，避免錯誤卻退款
        InvoiceNewRequest invoiceNewRequest = request.getInvoiceNewRequest();
        if (invoiceNewRequest != null) {
            invoiceService.invoiceValidateBeforeRecord(invoiceNewRequest.getInvoices(), priceInfoService.getPriceInfosByOrder(orderNo));
        }
        invoiceService.invoiceValidateAmountBeforeRecord(request.getPaymentRequest().getAccountRecords(), Optional.ofNullable(invoiceNewRequest).map(InvoiceNewRequest::getInvoices).orElseGet(ArrayList::new), orderNo);
        PaymentRes paymentRes = recordAccounts(request.getPaymentRequest(), orderNo);
        if (request.getInvoiceUpdateRequestList() != null) {
            for (InvoiceUpdateRequest invoiceUpdateRequest : request.getInvoiceUpdateRequestList()) {
                invoiceService.updateInvoice(orderNo, invoiceUpdateRequest.getInvoiceNo(), invoiceUpdateRequest.getReason(), memberId);
            }
        }
        if (invoiceNewRequest != null) {
            invoiceService.createInvoice(orderNo, invoiceNewRequest.getPayFor(), invoiceNewRequest.getInvoices(), memberId);
        }
        response.setPaymentRes(paymentRes);
        response.setInvoicesList(invoiceService.getInvoice(orderNo));
        return response;
    }

    /**
     * 帳務登打並新增/異動發票
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void accountSettlementForLegalOperation(AccountSettlementRequest request, String orderNo, String memberId) {
        AccountSettlementResponse response = new AccountSettlementResponse();
        // 先檢查發票，避免錯誤卻退款
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        invoiceService.invoiceValidateBeforeRecord(request.getInvoiceNewRequest().getInvoices(), priceInfoWrapper.getList());
        Orders order = orderService.getOrder(orderNo);
        // 訂閱訂單收支登打條件
        if (order.getStatus() < OrderStatus.BOOKING.getStatus()) {
            throw new BadRequestException("訂閱訂單未成立，不可登打收支");
        }

        List<AccountRecord> accountRecords = request.getPaymentRequest().getAccountRecords().stream()
            .filter(accountRecord -> OrderStatus.CANCEL.getStatus() == order.getStatus() || SecurityDeposit != accountRecord.getPayFor())
            .collect(Collectors.toList());

        Map<Long, Account> dbAccounts = getAccountsByOrders(order.getOrderNo()).stream()
            .filter(account1 -> !account1.isDeleted()).collect(Collectors.toMap(Account::getId, Function.identity()));
        // db accounts 已經有的金額
        int total = calculateTotal(accountRecords, dbAccounts);

        log.info("訂單: {}, 加總應收退總金額: {}", order.getOrderNo(), total);

        int receivedAmt = priceInfoWrapper.getCurrentReceivable().getActualPrice();
        if (order.getStatus() == OrderStatus.CANCEL.getStatus()) {
            receivedAmt += priceInfoWrapper.getByCategory(PriceInfoDefinition.PriceInfoCategory.SecurityDeposit).getActualReceivePrice();
        }
        log.info("訂單: {}, 應收總額: {}", order.getOrderNo(), receivedAmt);
        if (receivedAmt != total) {
            throw new BadRequestException("加總應收退款金額不等於應收總額");
        }

        updateAccountsAndDetails(order, accountRecords, dbAccounts);

        updateRemitAccount(order, new ArrayList<>(dbAccounts.values()), accountRecords);
        // 匯款退款金額設定
        setRemitReceiveAmount(order.getOrderNo(), accountRecords, priceInfoWrapper);
        // 發動退款
        refundByAccountRequest(order, accountRecords);
        // 等待Queue回來
        checkTappayRefundSuccess(order.getOrderNo(), accountRecords);
        List<Account> accounts = new ArrayList<>(dbAccounts.values());
        PaymentRes paymentRes = new PaymentRes(order, accounts.stream().filter(account -> !account.isDeleted()).sorted(Comparator.comparing(Account::getId)).collect(Collectors.toList()));
        if (request.getInvoiceUpdateRequestList() != null) {
            for (InvoiceUpdateRequest invoiceUpdateRequest : request.getInvoiceUpdateRequestList()) {
                invoiceService.updateInvoice(orderNo, invoiceUpdateRequest.getInvoiceNo(), invoiceUpdateRequest.getReason(), memberId);
            }
        }
        if (request.getInvoiceNewRequest() != null) {
            invoiceService.createInvoice(orderNo, request.getInvoiceNewRequest().getPayFor(), request.getInvoiceNewRequest().getInvoices(), memberId);
        }
        response.setPaymentRes(paymentRes);
        response.setInvoicesList(invoiceService.getInvoice(orderNo));
    }


    /**
     * 付款與折扣攤平
     */
    public void payAndDiscountBalance(String orderNo) {
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getUnPaidPriceInfoByOrder(orderNo, false);
        //  里程費若沒有結束里程不顯示
        orderPriceInfoList =
            orderPriceInfoList.stream().filter(orderPriceInfo -> !(orderPriceInfo.getCategory() == MileageFee && Optional.ofNullable(orderPriceInfo.getInfoDetail()).map(PriceInfoDetail::getEndMileage).orElse(0) == 0)).collect(Collectors.toList());
        if (!orderPriceInfoList.isEmpty()) {
            int amount = orderPriceInfoList.stream().mapToInt(PriceInfoInterface::getActualPrice).sum();
            if (amount == 0) {
                PaymentInfo paymentInfo = getPaymentsByOrder(orderNo).stream().filter(p -> p.getPaymentCategory() == PayAuth && p.getPayFor() == PayFor.Depart).findAny().orElse(null);
                if (paymentInfo != null) {
                    creditPayAndDiscountBalance(orderPriceInfoList, paymentInfo);
                } else {
                    accountPayAndDiscountBalance(orderNo, orderPriceInfoList);
                }
            }
            orderPriceInfoRepository.saveAll(orderPriceInfoList);
        }
    }

    /**
     * 網刷沖銷
     */
    private void creditPayAndDiscountBalance(List<OrderPriceInfo> orderPriceInfoList, PaymentInfo paymentInfo) {
        orderPriceInfoList.forEach(opi -> {
            // 若Etag費用未設定則不沖消
            if (opi.getCategory() == ETag) {
                ETagInfo eTagInfo = etagInfoRepository.getEtagInfoByOrderPriceId(opi.getId());
                if (eTagInfo.getETagAmt() == null) {
                    return;
                }
            }
            opi.setPaymentId(paymentInfo.getPaymentId());
            opi.setRecTradeId(paymentInfo.getTradeId());
            opi.setReceivedAmount(opi.getAmount());
        });
    }

    /**
     * 過往登打資訊沖銷
     */
    private void accountPayAndDiscountBalance(String orderNo, List<OrderPriceInfo> orderPriceInfoList) {
        List<Account> accounts = getAccountsByOrder(orderNo);
        if (accounts != null) {
            Account account = accounts.get(0);
            orderPriceInfoList.forEach(opi -> {
                if (account.getAccountType() == Credit) {
                    getPaymentInfosByTradeId(account.getTradeId()).stream().filter(p -> p.getPaymentCategory() == PayAuth).findAny().ifPresent(p -> {
                        opi.setPaymentId(p.getPaymentId());
                        opi.setRecTradeId(p.getTradeId());
                        opi.setReceivedAmount(opi.getAmount());
                    });

                } else if (account.getAccountType() == AccountType.Remit) {
                    opi.setRemitAccountIds(Collections.singletonList(account.getId()));
                    opi.setReceivedAmount(opi.getAmount());
                }

            });
        }
    }


    /**
     * 將Payment與OrderPriceInfo綁定
     * 因可能有重複付款，故需處理該重複付款問題
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void bindPriceInfo(String orderNo, AccountOrderPriceBindingRequest request) {
        List<OrderPriceInfo> orderPriceInfoList = orderPriceInfoRepository.findAllById(request.getOrderPriceInfoIds()).stream().filter(opi -> opi.getOrderNo().equals(orderNo)).collect(Collectors.toList());
        PaymentInfo paymentInfo = paymentInfoRepository.findById(request.getPaymentId()).orElseThrow(() -> new BadRequestException("找不到該Payment"));
        if (!paymentInfo.getOrderId().equals(orderNo)) {
            throw new BadRequestException("付款訂單編號與指定訂單編號不一致");
        }
        if (request.getOrderPriceInfoIds().size() != orderPriceInfoList.size()) {
            throw new BadRequestException("DB明細數量與送出來的數量不一致");
        }
        if (orderPriceInfoList.stream().mapToInt(OrderPriceInfo::getAmount).sum() != paymentInfo.getAmount()) {
            throw new BadRequestException("DB明細總金額與付款金額不同");
        }
        for (OrderPriceInfo opi : orderPriceInfoList) {
            opi.setReceivedAmount(opi.getAmount());
            opi.setPaymentId(request.getPaymentId());
        }
    }


    public void refundByAccountRequest(Orders orders, List<AccountRecord> accountRecords) {
        List<AccountRecord> refundList = accountRecords.stream().filter(r -> r.getRefundAmount() > 0).collect(Collectors.toList());
        List<OrderPriceInfo> refundInfos = new ArrayList<>();
        if (!refundList.isEmpty()) {
            List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getUnPaidPriceInfoByOrder(orders.getOrderNo(), false);
            for (AccountRecord accountRecord : refundList) {
                // 匯款的不執行網刷退款
                if (accountRecord.getAccountType() == Remit) {
                    continue;
                }
                List<OrderPriceInfo> refundOrPrices = getRefundOrPrices(orderPriceInfoList, accountRecord.getTradeId());

                int amount = refundOrPrices.stream().mapToInt(OrderPriceInfo::getAmount).sum();
                if (!accountRecord.getRefundAmount().equals(amount)) {
                    throw new BadRequestException(String.format("登打的[網刷]退款金額與費用明細退款金額不一致,登打金額：%d ，費用明細金額：%d", accountRecord.getRefundAmount(), amount));
                }
                refundInfos.addAll(orderPriceInfoList);
            }
        }
        if (!refundInfos.isEmpty()) {
            refundOrderPriceInfosRetry(refundInfos.stream().map(OrderPriceInfo::getId).collect(Collectors.toList()));
        }
    }

    private List<OrderPriceInfo> getRefundOrPrices(List<OrderPriceInfo> orderPriceInfoList, String tradeId) {
        return orderPriceInfoList.stream().filter(opi -> Objects.equals(opi.getRecTradeId(), tradeId)
            && opi.getType() == Refund.getCode()
            && opi.getReceivedAmount() == 0
            && opi.getPaymentId() == null
            && (StringUtils.isBlank(opi.getUid()) || StringUtils.isNotBlank(opi.getUid())
            && Optional.ofNullable(opi.getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(false))
        ).collect(Collectors.toList());
    }

    /**
     * 匯款退款
     */
    @Transactional
    public void setRemitReceiveAmount(String orderNo, List<AccountRecord> accountRecords, PriceInfoWrapper priceInfoWrapper) {
        List<AccountRecord> refundList = accountRecords.stream().filter(r -> r.getRefundAmount() > 0 && r.getAccountType() == Remit).collect(Collectors.toList());

        if (!refundList.isEmpty()) {
            int refundAmt = 0;
            int refundAccountAmt = 0;
            PriceInfoWrapper unpaidRefundWrapper = priceInfoWrapper.getUnpaid().getByType(Refund).getPaidRemit();
            for (AccountRecord accountRecord : refundList) {
                List<OrderPriceInfo> refundOrPrices = unpaidRefundWrapper.getList().stream()
                    .filter(opi -> opi.getRemitAccountIds().contains(accountRecord.getId())
                        && (StringUtils.isBlank(opi.getUid()) || StringUtils.isNotBlank(opi.getUid()) && Optional.ofNullable(opi.getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(false)))
                    .collect(Collectors.toList());

                int amount = refundOrPrices.stream().mapToInt(OrderPriceInfo::getAmount).sum();
                refundAmt += amount;
                refundAccountAmt += accountRecord.getRefundAmount();
                refundOrPrices.forEach(opi -> opi.setReceivedAmount(opi.getAmount()));
                orderPriceInfoRepository.saveAll(refundOrPrices);
            }
            if (refundAccountAmt != refundAmt) {
                throw new BadRequestException(String.format("登打的[匯款]退款金額與費用明細退款金額不一致,登打金額：%d ，費用明細金額：%d", refundAccountAmt, refundAmt));
            }
        }
    }

    /**
     * 收支燈打須等tappay回傳
     */
    public void checkTappayRefundSuccess(String orderNo, List<AccountRecord> accountRecords) {
        Orders orders = orderService.getOrder(orderNo);
        List<AccountRecord> refundList = accountRecords.stream().filter(r -> r.getRefundAmount() > 0 && r.getAccountType() == Credit).collect(Collectors.toList());
        List<OrderPriceInfo> refundInfos = new ArrayList<>();
        if (!refundList.isEmpty()) {
            List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrderWithNewTransaction(orders.getOrderNo());
            for (AccountRecord accountRecord : refundList) {
                List<OrderPriceInfo> refundOrPrices = getRefundOrPrices(orderPriceInfoList, accountRecord.getTradeId());
                refundInfos.addAll(refundOrPrices);
            }
        }
        List<Integer> refundIds = refundInfos.stream().map(OrderPriceInfo::getId).collect(Collectors.toList());
        AtomicBoolean isRefund = new AtomicBoolean(false);
        int count = 0;
        log.info("開始檢查Payment退款是否已回傳Queue,OrderNos= {},refundInfos={}", orderNo, refundInfos.stream().map(OrderPriceInfo::getId).collect(Collectors.toList()));
        if (refundInfos.isEmpty()) {
            isRefund.set(true);
        }
        while (!refundInfos.isEmpty() && !isRefund.get() && count < 30) {
            isRefund.set(true);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            refundInfos = priceInfoService.getPriceInfosByOrderWithNewTransaction(orders.getOrderNo()).stream().filter(opi -> refundIds.contains(opi.getId())).collect(Collectors.toList());
            refundInfos.forEach(opi -> {
                if (opi.getReceivedAmount() == 0) {
                    isRefund.set(false);
                }
            });
            count++;
        }
        if (!isRefund.get()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("OrderNo", orderNo);
            map.put("count", count);
            map.put("accountRecords", accountRecords);
            mattermostServer.notify("登打發動退款等不到Payment回應", map, null);
        }
    }

    /**
     * 出車時通知財務中台正式出車車號
     */
    @Async
    public void changeAccountSecurityDepositCar(String orderNo, String plateNo) {
        SecurityChangeCarReq req = SecurityChangeCarReq.builder()
            .clientId(8)
            .departmentCode(CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE)
            .carNumber(plateNo)
            .orderNumber(orderNo)
            .transactionItemCode(TransactionItemCodeEnum.MARGIN.name())
            .build();
        finServiceBusClient.securityDepositChangeCar(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, req);
    }

    public PaymentInfoQueryResponse updatePaymentRemark(int paymentId, String remark, String memberId) {
        PaymentInfo paymentInfo = paymentInfoRepository.findById(paymentId).orElseThrow(() -> new SubscribeException(PAYMENT_INFO_NOT_FOUND));
        paymentInfo.setRemark(remark);
        paymentInfo.setRemarker(memberId);
        paymentInfoRepository.save(paymentInfo);
        PaymentInfoQueryRequest request = new PaymentInfoQueryRequest();
        request.setPaymentId(paymentId);
        return searchByPage(new PageRequest(1, 0), request).getList().get(0);
    }

    private void remitMaxAmountValidate(List<AccountRecord> accountRecords, Date createDate) {
        Map<Long, Integer> remitAmtMap = accountRecords.stream().filter(account -> account.getAccountType() == Remit).collect(Collectors.groupingBy(AccountRecord::getRemitNo, Collectors.summingInt(AccountRecord::getOriginalAmount)));
        for (Map.Entry<Long, Integer> entry : remitAmtMap.entrySet()) {
            ICBCRep icbc = getRemitInfo(entry.getKey(), createDate);
            if (icbc.getTxAmount() != null && entry.getValue() > icbc.getTxAmount()) {
                throw new BadRequestException(String.format("匯款金額超過上限, remitNo: %d, 金額: %d, 上限: %d", entry.getKey(), entry.getValue(), icbc.getTxAmount()));
            }
        }
    }

    private ICBCRep getRemitInfo(Long remitNo, Date createDate) {
        String format = "yyyy/MM/dd";
        String startDate = DateUtils.toDateString(DateUtils.toDate(DateUtils.toLocalDateTime(createDate).minus(1, ChronoUnit.YEARS)), format);
        String endDate = DateUtils.toDateString(DateUtils.toDate(DateUtils.toLocalDateTime(new Date()).plus(7, ChronoUnit.DAYS)), format);
        Result<List<ICBCRep>> result = financeClient.getRemitInfo("", startDate, endDate, "", remitNo.toString());
        if (result.getStatusCode() != 0 || result.getData() == null || result.getData().isEmpty()) {
            throw new SubscribeException(CAN_NOT_FIND_BY_REMIT_NO);
        }
        return result.getData().stream().filter(icbc -> icbc.getICBCAuto().equals(remitNo)).findFirst().orElseThrow(() -> new SubscribeException(CAN_NOT_FIND_BY_REMIT_NO));
    }
}
